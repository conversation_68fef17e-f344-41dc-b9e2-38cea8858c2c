import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:lucide_icons/lucide_icons.dart';
import '../../../core/services/ai_service.dart';
import '../../../core/services/permission_service.dart';
import '../../../core/services/geolocation_service.dart';
import '../../../core/models/quick_add_response.dart';
import '../../tasks/providers/task_provider.dart';
import '../../tasks/models/task.dart';
// Import recording modals
import './recording_modal.dart';
import './continuous_recording_modal.dart';

/// Bottom sheet for quick adding tasks or groceries
class QuickAddSheet extends ConsumerStatefulWidget {
  const QuickAddSheet({super.key});

  @override
  ConsumerState<QuickAddSheet> createState() => _QuickAddSheetState();
}

class _QuickAddSheetState extends ConsumerState<QuickAddSheet> {
  final _textController = TextEditingController();
  bool _isProcessing = false;

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  /// Submit the quick add text to the AI service
  Future<void> _submitQuickAdd() async {
    final text = _textController.text.trim();
    if (text.isEmpty || _isProcessing) return;

    setState(() => _isProcessing = true);

    // Get location
    final coordinates = await ref.read(geolocationServiceProvider).getCurrentCoordinates();

    try {
      final result = await ref.read(aiServiceProvider).processQuickAdd(
        text,
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      );

      if (mounted) {
        Navigator.pop(context); // Close sheet on success/failure handled by toast

        // Show appropriate toast
        if (result.type == QuickAddResponseType.tasks && result.data.isNotEmpty) {
          // Convert task data to proper TaskListItem objects to ensure fields are mapped correctly
          final List<TaskListItem> newTasks = [];
          for (final taskData in result.data) {
            // Ensure each task has the correct data structure
            try {
              final task = TaskListItem.fromJson(taskData);
              newTasks.add(task);
              // Debug the priority field
              print('📊 QuickAdd created task: ${task.id} with priority: ${task.priority}');
            } catch (e) {
              print('❌ Error parsing task from QuickAdd: $e');
            }
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Created ${result.data.length} task(s)'),
              backgroundColor: Colors.green,
            ),
          );
          // Force a full refresh to ensure task data is up-to-date
          ref.read(taskProvider.notifier).refresh();
        } else if (result.type == QuickAddResponseType.groceries && result.data.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Added ${result.data.length} grocery item(s)'),
              backgroundColor: Colors.green,
            ),
          );
          // Refresh groceries when grocery provider is available
          // ref.read(groceryProvider.notifier).refresh();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message ?? 'Input processed, no action taken.'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
        Navigator.pop(context); // Close sheet on error
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  /// Handle voice input button press (regular mode)
  Future<void> _handleVoiceInput() async {
    await _openRecordingModal(continuous: false);
  }

  /// Handle voice input button long press (continuous mode)
  Future<void> _handleContinuousVoiceInput() async {
    await _openRecordingModal(continuous: true);
  }

  /// Open the appropriate recording modal
  Future<void> _openRecordingModal({required bool continuous}) async {
    final permissionService = ref.read(permissionServiceProvider);
    final micPermissionGranted = await permissionService.requestMicrophonePermission();

    if (!micPermissionGranted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Microphone permission denied.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // Show info about continuous mode if enabled
    if (continuous && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Starting continuous recording mode...'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 1),
        ),
      );
    }

    // Open appropriate Recording Modal
    if (mounted) {
      final transcript = await showDialog<String>(
        context: context,
        barrierDismissible: false, // Prevent closing by tapping outside
        builder: (_) => continuous
            ? const ContinuousRecordingModal()
            : const RecordingModal(),
      );

      if (transcript != null && transcript.isNotEmpty && mounted) {
        print('[QuickAddSheet] Transcript received: $transcript');
        _textController.text = transcript; // Prefill text field
        await _submitQuickAdd(); // Submit automatically
      } else {
        print('[QuickAddSheet] Recording cancelled or no transcript received.');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Make sheet content adjust to keyboard
    return Padding(
      padding: MediaQuery.of(context).viewInsets, // Adjusts for keyboard
      child: Container(
        padding: const EdgeInsets.all(20.0),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _textController,
                decoration: const InputDecoration(hintText: 'Type task or grocery...'),
                autofocus: true,
                enabled: !_isProcessing,
                onSubmitted: (_) => _submitQuickAdd(),
              ),
            ),
            const Gap(8),
            GestureDetector(
              onTap: _isProcessing ? null : _handleVoiceInput,
              onLongPress: _isProcessing ? null : _handleContinuousVoiceInput,
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _isProcessing
                      ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.12)
                      : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                ),
                child: Icon(
                  LucideIcons.mic,
                  color: _isProcessing
                      ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.38)
                      : Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const Gap(8),
            IconButton(
              icon: _isProcessing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(LucideIcons.send),
              onPressed: _isProcessing ? null : _submitQuickAdd,
              tooltip: 'Add',
              color: Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }
}
