
/// Interface for speech recognition service
/// This abstract class defines methods that both mobile and web implementations must provide
abstract class SpeechServiceInterface {
  /// Initialize the speech recognition service
  Future<bool> initialize();

  /// Start listening for speech input
  Future<bool> startListening({
    Function(String text)? onResult,
    Function(String status)? onStatus,
    Function(String error)? onError,
    Function(double level)? onSoundLevel,
  });

  /// Start continuous listening with seamless restart capability
  Future<bool> startContinuousListening({
    Function(String finalText, String partialText)? onTextUpdate,
    Function(String status)? onStatus,
    Function(String error)? onError,
    Function(double level)? onSoundLevel,
    Function()? onSilenceDetected,
  });

  /// Stop listening for speech input
  Future<void> stopListening();

  /// Check if speech recognition is available
  Future<bool> isAvailable();

  /// Check if speech recognition is currently active
  bool get isListening;

  /// Check if continuous recognition is active
  bool get isContinuousListening;

  /// Get the final recognized text
  String get finalText;

  /// Get the current partial text
  String get partialText;

  /// Get the combined text (final + partial)
  String get combinedText;

  /// Dispose resources
  void dispose();
}