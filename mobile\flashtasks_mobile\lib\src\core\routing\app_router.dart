import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/providers/auth_provider.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/splash_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/dashboard/screens/dashboard_shell.dart';
import '../../features/tasks/screens/task_list_screen.dart';
import '../../features/tasks/screens/task_detail_screen.dart';
import '../../features/tasks/screens/task_form_screen.dart';
import '../../features/insights/screens/insights_screen.dart';
import '../../features/insights/screens/review_duplicates_screen.dart';
import '../../features/insights/screens/merge_tasks_screen.dart';
import '../../features/groceries/screens/groceries_screen.dart';
import '../../features/locations/screens/locations_screen.dart';
import '../../features/debug/screens/error_test_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../features/settings/screens/profile_settings_screen.dart';
import '../../features/settings/screens/account_settings_screen.dart';
import '../../features/settings/screens/task_settings_screen.dart';
import '../../features/settings/screens/notification_settings_screen.dart';
import '../../features/settings/screens/datetime_settings_screen.dart';
import '../../features/settings/screens/keytag_mapping_screen_fixed.dart' as keytag_mapping;
import '../../features/settings/screens/category_location_mapping_settings_screen.dart';
import '../../features/settings/screens/location_suggestion_settings_screen.dart';
import '../../features/settings/screens/grocery_preferences_screen.dart';
import '../../features/settings/screens/server_settings_screen.dart';
import '../../features/reminders/presentation/test_reminder_page.dart';
import '../../features/notification_test_screen.dart';
import '../../features/dashboard/pages/speech_test_page.dart';

/// Provider that gives access to the app router
final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);

  return GoRouter(
    // Show transitions on route changes
    // debugLogDiagnostics: true,
    redirect: (BuildContext context, GoRouterState state) {
      // Check if the user is logged in
      final bool loggedIn = authState.isAuthenticated;

      // Get the current path
      final String path = state.uri.toString();

      // Allow access to server settings and test screens without authentication
      if (path.contains('/dashboard/settings/server') ||
          path.contains('/test-reminders') ||
          path.contains('/notification-test') ||
          path.contains('/speech-test')) {
        return null;  // No redirect, allow direct access
      }

      // If the user is not logged in and not on the login or register screen, redirect to the login screen
      if (!loggedIn && path != '/login' && path != '/register' && !path.startsWith('/splash')) {
        return '/login';
      }

      // If the user is logged in and on the login screen, redirect to the dashboard
      if (loggedIn && (path == '/login' || path == '/')) {
        return '/dashboard/home';
      }

      // Otherwise, no redirect is necessary
      return null;
    },
    routes: [
      // Auth routes
      GoRoute(
        path: '/',
        redirect: (_, __) => '/splash',
      ),
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RegisterScreen(),
      ),

      // Direct access routes (outside of ShellRoute for easier access)
      GoRoute(
        path: '/dashboard/settings/server',
        builder: (context, state) => const ServerSettingsScreen(),
      ),

      // Test reminder page
      GoRoute(
        path: '/test-reminders',
        builder: (context, state) => const TestReminderPage(),
      ),

      // Notification test screen
      GoRoute(
        path: '/notification-test',
        builder: (context, state) => const NotificationTestScreen(),
      ),

      // Speech recognition test screen
      GoRoute(
        path: '/speech-test',
        builder: (context, state) => const SpeechTestPage(),
      ),

      // Dashboard route with sub-routes
      ShellRoute(
        builder: (context, state, child) {
          return DashboardShell(child: child);
        },
        routes: [
          // Home route
          GoRoute(
            path: '/dashboard/home',
            builder: (context, state) => const TaskListScreen(),
            // Task routes
            routes: [
              // Create task route - must come before the :taskId route to avoid conflicts
              GoRoute(
                path: 'task/create',
                name: 'createTask',
                builder: (context, state) => const TaskFormScreen(
                  isEditing: false,
                ),
              ),
              // Task detail route
              GoRoute(
                path: 'task/:taskId',
                name: 'taskDetail',
                builder: (context, state) => TaskDetailScreen(
                  taskId: state.pathParameters['taskId']!,
                ),
              ),
              // Edit task route
              GoRoute(
                path: 'task/:taskId/edit',
                name: 'editTask',
                builder: (context, state) => TaskFormScreen(
                  isEditing: true,
                  taskId: state.pathParameters['taskId']!,
                ),
              ),
            ],
          ),

          // Insights route
          GoRoute(
            path: '/dashboard/insights',
            builder: (context, state) => const InsightsScreen(),
            routes: [
              // Review duplicates route
              GoRoute(
                path: 'review-duplicates',
                name: 'reviewDuplicates',
                builder: (context, state) {
                  final String taskIdsStr = state.uri.queryParameters['taskIds'] ?? '';
                  final String insightId = state.uri.queryParameters['insightId'] ?? '';

                  // Convert comma-separated task IDs string back to List
                  final List<String> taskIds = taskIdsStr.split(',');

                  return ReviewDuplicatesScreen(
                    taskIds: taskIds,
                    insightId: insightId,
                  );
                },
              ),
              // Merge tasks route
              GoRoute(
                path: 'merge-tasks',
                name: 'mergeTasks',
                builder: (context, state) {
                  final String taskIdsStr = state.uri.queryParameters['taskIds'] ?? '';
                  final String insightId = state.uri.queryParameters['insightId'] ?? '';

                  // Convert comma-separated task IDs string back to List
                  final List<String> taskIds = taskIdsStr.split(',');

                  return MergeTasksScreen(
                    taskIds: taskIds,
                    insightId: insightId,
                  );
                },
              ),
            ],
          ),

          // Groceries route
          GoRoute(
            path: '/dashboard/groceries',
            builder: (context, state) => const GroceriesScreen(),
          ),

          // Locations route
          GoRoute(
            path: '/dashboard/locations',
            builder: (context, state) => const LocationsScreen(),
          ),

          // Settings route
          GoRoute(
            path: '/dashboard/settings',
            builder: (context, state) => const SettingsScreen(),
            routes: [
              GoRoute(
                path: 'profile',
                name: 'profileSettings',
                builder: (context, state) => const ProfileSettingsScreen(),
              ),
              GoRoute(
                path: 'account',
                name: 'accountSettings',
                builder: (context, state) => const AccountSettingsScreen(),
              ),
              GoRoute(
                path: 'tasks',
                name: 'taskSettings',
                builder: (context, state) => const TaskSettingsScreen(),
              ),
              GoRoute(
                path: 'groceries',
                name: 'groceryPreferences',
                builder: (context, state) => const GroceryPreferencesScreen(),
              ),
              GoRoute(
                path: 'notifications',
                name: 'notificationSettings',
                builder: (context, state) => const NotificationSettingsScreen(),
              ),
              GoRoute(
                path: 'datetime',
                name: 'dateTimeSettings',
                builder: (context, state) => const DateTimeSettingsScreen(),
              ),
              GoRoute(
                path: 'keytags',
                name: 'keytagMappingSettings',
                builder: (context, state) => const keytag_mapping.KeytagMappingSettingsScreen(),
              ),
              GoRoute(
                path: 'category-location-mappings',
                name: 'categoryLocationMappingSettings',
                builder: (context, state) => const CategoryLocationMappingSettingsScreen(),
              ),
              GoRoute(
                path: 'location-suggestions',
                name: 'locationSuggestionSettings',
                builder: (context, state) => const LocationSuggestionSettingsScreen(),
              ),
              // Keep this route for consistency, as it's also accessible directly
              GoRoute(
                path: 'server',
                name: 'serverSettings',
                builder: (context, state) => const ServerSettingsScreen(),
              ),
            ],
          ),

          // Debug routes (only in development)
          GoRoute(
            path: '/dashboard/debug/error-test',
            builder: (context, state) => const ErrorTestScreen(),
          ),
        ],
      ),
    ],
  );
});
