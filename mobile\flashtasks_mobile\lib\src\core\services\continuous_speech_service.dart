import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_recognition_error.dart';

import 'speech_service_interface.dart';

/// Continuous speech recognition service with seamless restart capability
/// Inspired by Android's ContinuousSpeechRecognizer pattern
class ContinuousSpeechService implements SpeechServiceInterface {
  final SpeechToText _speech = SpeechToText();
  
  // Core state management
  bool _isListening = false;
  bool _isContinuousListening = false;
  bool _shouldContinue = true;
  bool _isRestarting = false;
  
  // Text management
  String _finalText = '';
  String _partialText = '';
  String _lastConfirmedText = '';
  
  // Timing and silence detection
  DateTime _lastSpeechTime = DateTime.now();
  Timer? _restartTimer;
  Timer? _silenceTimer;
  
  // Callbacks for continuous mode
  Function(String finalText, String partialText)? _onTextUpdateCallback;
  Function(String)? _onStatusCallback;
  Function(String)? _onErrorCallback;
  Function(double)? _onSoundLevelCallback;
  Function()? _onSilenceDetectedCallback;
  
  // Constants for seamless experience
  static const int restartDelayMs = 100; // Minimal delay for restart
  static const int maxSilenceDurationMs = 3000; // 3 seconds of silence detection
  static const int silenceCheckIntervalMs = 500; // Check every 500ms
  
  // Keep track of the max sound level to normalize values
  double _maxSoundLevel = 1.0;

  @override
  String get finalText => _finalText;

  @override
  String get partialText => _partialText;

  @override
  String get combinedText {
    if (_finalText.isEmpty) return _partialText;
    if (_partialText.isEmpty) return _finalText;
    return '$_finalText $_partialText';
  }

  @override
  bool get isListening => _isListening;

  @override
  bool get isContinuousListening => _isContinuousListening;

  @override
  Future<bool> initialize() async {
    try {
      final bool available = await _speech.initialize(
        onError: (SpeechRecognitionError error) {
          debugPrint('Speech initialization error: ${error.errorMsg}, permanent: ${error.permanent}');
          _handleError('Initialization error: ${error.errorMsg}');
        },
        debugLogging: kDebugMode,
        onStatus: (status) {
          debugPrint('Speech status from initialize: $status');
          _handleStatus(status);
        },
      );
      return available;
    } catch (e) {
      debugPrint('Error initializing speech recognition: $e');
      return false;
    }
  }

  @override
  Future<bool> startListening({
    Function(String text)? onResult,
    Function(String status)? onStatus,
    Function(String error)? onError,
    Function(double level)? onSoundLevel,
  }) async {
    // Reset for single session
    _finalText = '';
    _partialText = '';
    _maxSoundLevel = 1.0;
    _onStatusCallback = onStatus;
    _onSoundLevelCallback = onSoundLevel;

    try {
      await _speech.listen(
        onResult: (SpeechRecognitionResult result) {
          debugPrint('Speech result: "${result.recognizedWords}", final: ${result.finalResult}');
          _finalText = result.recognizedWords;
          if (onResult != null) {
            onResult(result.recognizedWords);
          }
        },
        onSoundLevelChange: (level) {
          _maxSoundLevel = level > _maxSoundLevel ? level : _maxSoundLevel;
          final normalizedLevel = _maxSoundLevel > 0 ? level / _maxSoundLevel : 0.0;
          if (onSoundLevel != null) {
            onSoundLevel(normalizedLevel);
          }
        },
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: false,
          listenMode: ListenMode.dictation,
          enableHapticFeedback: true,
        ),
      );
      return true;
    } catch (e) {
      debugPrint('Error starting speech recognition: $e');
      if (onError != null) {
        onError('Failed to start speech recognition: $e');
      }
      return false;
    }
  }

  @override
  Future<bool> startContinuousListening({
    Function(String finalText, String partialText)? onTextUpdate,
    Function(String status)? onStatus,
    Function(String error)? onError,
    Function(double level)? onSoundLevel,
    Function()? onSilenceDetected,
  }) async {
    // Store callbacks for continuous mode
    _onTextUpdateCallback = onTextUpdate;
    _onStatusCallback = onStatus;
    _onErrorCallback = onError;
    _onSoundLevelCallback = onSoundLevel;
    _onSilenceDetectedCallback = onSilenceDetected;
    
    // Reset state for continuous session
    _finalText = '';
    _partialText = '';
    _lastConfirmedText = '';
    _maxSoundLevel = 1.0;
    _shouldContinue = true;
    _isRestarting = false;
    _isContinuousListening = true;
    _lastSpeechTime = DateTime.now();
    
    // Start the continuous listening cycle
    return await _beginContinuousListening();
  }

  /// Begin the continuous listening cycle
  Future<bool> _beginContinuousListening() async {
    if (!_shouldContinue || _isRestarting) {
      return false;
    }

    try {
      await _speech.listen(
        onResult: _handleContinuousResult,
        onSoundLevelChange: _handleSoundLevelChange,
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: false,
          listenMode: ListenMode.dictation,
          enableHapticFeedback: true,
        ),
      );
      
      _isListening = true;
      _handleStatus('listening');
      _startSilenceDetection();
      
      return true;
    } catch (e) {
      debugPrint('Error starting continuous listening: $e');
      _handleError('Failed to start continuous listening: $e');
      return false;
    }
  }

  /// Handle results in continuous mode
  void _handleContinuousResult(SpeechRecognitionResult result) {
    debugPrint('Continuous result: "${result.recognizedWords}", final: ${result.finalResult}');
    
    if (result.finalResult) {
      // Final result - add to permanent text
      if (result.recognizedWords.trim().isNotEmpty) {
        String newText = _cleanAndDeduplicateText(result.recognizedWords);
        if (newText.trim().isNotEmpty) {
          if (_finalText.isNotEmpty) {
            _finalText = '$_finalText $newText';
          } else {
            _finalText = newText;
          }
          _lastConfirmedText = newText;
        }
      }
      
      _partialText = '';
      _updateUI();
      
      // Restart for continuous recognition
      if (_shouldContinue && _isContinuousListening) {
        _restartListening();
      }
    } else {
      // Partial result - update current partial text
      _partialText = result.recognizedWords;
      _updateUI();
      _lastSpeechTime = DateTime.now();
    }
  }

  /// Clean and deduplicate text to prevent repetition
  String _cleanAndDeduplicateText(String newText) {
    if (_lastConfirmedText.isNotEmpty) {
      String finalTextLower = _finalText.toLowerCase();
      String newTextLower = newText.toLowerCase();
      String lastConfirmedLower = _lastConfirmedText.toLowerCase();
      
      // If the new text starts with the last confirmed text, remove the duplicate
      if (newTextLower.startsWith(lastConfirmedLower)) {
        newText = newText.substring(_lastConfirmedText.length).trim();
      }
    }
    
    return newText.trim();
  }

  /// Restart listening with minimal delay for seamless experience
  void _restartListening() {
    if (!_shouldContinue || _isRestarting || !_isContinuousListening) {
      return;
    }
    
    _isRestarting = true;
    _isListening = false;
    
    // Cancel any pending timers
    _silenceTimer?.cancel();
    
    // Restart with minimal delay
    _restartTimer = Timer(Duration(milliseconds: restartDelayMs), () {
      if (_shouldContinue && _isContinuousListening) {
        _isRestarting = false;
        _beginContinuousListening();
      }
    });
  }

  /// Start silence detection
  void _startSilenceDetection() {
    _silenceTimer?.cancel();
    _silenceTimer = Timer.periodic(
      Duration(milliseconds: silenceCheckIntervalMs),
      _checkForSilence,
    );
  }

  /// Check for extended silence periods
  void _checkForSilence(Timer timer) {
    if (!_isListening || !_shouldContinue || !_isContinuousListening) {
      timer.cancel();
      return;
    }
    
    final silenceDuration = DateTime.now().difference(_lastSpeechTime).inMilliseconds;
    
    if (silenceDuration > maxSilenceDurationMs && _partialText.trim().isEmpty) {
      // Extended silence detected
      if (_onSilenceDetectedCallback != null) {
        _onSilenceDetectedCallback!();
      }
    }
  }

  /// Handle sound level changes
  void _handleSoundLevelChange(double level) {
    _maxSoundLevel = level > _maxSoundLevel ? level : _maxSoundLevel;
    final normalizedLevel = _maxSoundLevel > 0 ? level / _maxSoundLevel : 0.0;
    
    // Update last speech time when sound is detected
    if (level > 0.1) { // Adjust threshold as needed
      _lastSpeechTime = DateTime.now();
    }
    
    if (_onSoundLevelCallback != null) {
      _onSoundLevelCallback!(normalizedLevel);
    }
  }

  /// Handle status changes
  void _handleStatus(String status) {
    debugPrint('Continuous speech status: $status');
    
    if (_onStatusCallback != null) {
      _onStatusCallback!(status);
    }
    
    // Handle different status types for continuous mode
    if (status == 'notListening' || status == 'done') {
      if (_isContinuousListening && _shouldContinue) {
        // Speech recognition stopped - restart automatically
        _restartListening();
      } else {
        _isListening = false;
      }
    }
  }

  /// Handle errors with smart recovery
  void _handleError(String error) {
    debugPrint('Continuous speech error: $error');
    
    if (_onErrorCallback != null) {
      _onErrorCallback!(error);
    }
    
    // Attempt recovery for continuous mode
    if (_isContinuousListening && _shouldContinue) {
      // Delay restart for error recovery
      Timer(Duration(milliseconds: 1000), () {
        if (_shouldContinue && _isContinuousListening) {
          _restartListening();
        }
      });
    }
  }

  /// Update UI with current text state
  void _updateUI() {
    if (_onTextUpdateCallback != null) {
      _onTextUpdateCallback!(_finalText, _partialText);
    }
  }

  @override
  Future<void> stopListening() async {
    _shouldContinue = false;
    _isContinuousListening = false;
    _isListening = false;
    
    // Cancel all timers
    _restartTimer?.cancel();
    _silenceTimer?.cancel();
    
    await _speech.stop();
    
    _handleStatus('stopped');
  }

  @override
  Future<bool> isAvailable() async {
    return _speech.isAvailable;
  }

  @override
  void dispose() {
    stopListening();
    _restartTimer?.cancel();
    _silenceTimer?.cancel();
    _speech.cancel();
  }
}
