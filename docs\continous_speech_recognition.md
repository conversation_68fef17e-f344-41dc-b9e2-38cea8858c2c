public class ContinuousSpeechRecognizer implements RecognitionListener {
    private static final String TAG = "ContinuousSpeechRecognizer";
    private static final int RESTART_DELAY_MS = 100; // Minimal delay for restart
    private static final int MAX_SILENCE_DURATION_MS = 2000; // Stop after 2 seconds of silence
    
    // Core components
    private SpeechRecognizer speechRecognizer;
    private Intent recognizerIntent;
    private Context context;
    private Handler mainHandler;
    private Handler backgroundHandler;
    private HandlerThread backgroundThread;
    
    // State management
    private boolean isListening = false;
    private boolean shouldContinue = true;
    private boolean isRestarting = false;
    private long lastSpeechTime = 0;
    
    // Text management
    private StringBuilder finalText = new StringBuilder();
    private String currentPartialText = "";
    private String lastConfirmedText = "";
    
    // Callback interface
    public interface SpeechRecognitionCallback {
        void onTextUpdate(String finalText, String partialText);
        void onError(String error);
        void onStatusChange(boolean isListening);
        void onSilenceDetected();
    }
    
    private SpeechRecognitionCallback callback;
    
    public ContinuousSpeechRecognizer(Context context, SpeechRecognitionCallback callback) {
        this.context = context;
        this.callback = callback;
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        // Create background thread for non-UI operations
        backgroundThread = new HandlerThread("SpeechRecognitionThread");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
        
        initializeSpeechRecognizer();
    }
    
    private void initializeSpeechRecognizer() {
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
        }
        
        speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context);
        speechRecognizer.setRecognitionListener(this);
        
        // Configure recognition intent for optimal continuous recognition
        recognizerIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, 
            RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, 
            context.getPackageName());
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1);
        
        // These extras may help with longer recognition sessions
        recognizerIntent.putExtra("android.speech.extra.DICTATION_MODE", true);
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_PREFER_OFFLINE, false);
        
        // Language setting (adjust as needed)
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault());
    }
    
    public void startListening() {
        if (!isListening && shouldContinue) {
            finalText.setLength(0); // Clear previous session text
            currentPartialText = "";
            lastConfirmedText = "";
            lastSpeechTime = System.currentTimeMillis();
            
            shouldContinue = true;
            isRestarting = false;
            
            beginListening();
        }
    }
    
    private void beginListening() {
        backgroundHandler.post(() -> {
            try {
                if (speechRecognizer != null && shouldContinue) {
                    speechRecognizer.startListening(recognizerIntent);
                    isListening = true;
                    
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onStatusChange(true);
                        }
                    });
                    
                    // Start silence detection
                    startSilenceDetection();
                }
            } catch (Exception e) {
                handleError("Failed to start listening: " + e.getMessage());
            }
        });
    }
    
    private void startSilenceDetection() {
        backgroundHandler.postDelayed(silenceChecker, 500);
    }
    
    private final Runnable silenceChecker = new Runnable() {
        @Override
        public void run() {
            if (isListening && shouldContinue) {
                long silenceDuration = System.currentTimeMillis() - lastSpeechTime;
                
                if (silenceDuration > MAX_SILENCE_DURATION_MS && 
                    currentPartialText.trim().isEmpty()) {
                    // Extended silence detected
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onSilenceDetected();
                        }
                    });
                } else {
                    // Continue checking
                    backgroundHandler.postDelayed(this, 500);
                }
            }
        }
    };
    
    public void stopListening() {
        shouldContinue = false;
        isListening = false;
        
        backgroundHandler.removeCallbacks(silenceChecker);
        
        if (speechRecognizer != null) {
            speechRecognizer.stopListening();
        }
        
        if (callback != null) {
            callback.onStatusChange(false);
        }
    }
    
    private void restartListening() {
        if (!shouldContinue || isRestarting) {
            return;
        }
        
        isRestarting = true;
        isListening = false;
        
        // Remove any pending silence checks
        backgroundHandler.removeCallbacks(silenceChecker);
        
        // Restart with minimal delay for seamless experience
        backgroundHandler.postDelayed(() -> {
            if (shouldContinue) {
                isRestarting = false;
                beginListening();
            }
        }, RESTART_DELAY_MS);
    }
    
    // RecognitionListener implementation
    @Override
    public void onReadyForSpeech(Bundle params) {
        Log.d(TAG, "Ready for speech");
    }
    
    @Override
    public void onBeginningOfSpeech() {
        Log.d(TAG, "Beginning of speech");
        lastSpeechTime = System.currentTimeMillis();
    }
    
    @Override
    public void onRmsChanged(float rmsdB) {
        // Update last speech time when sound is detected
        if (rmsdB > -30) { // Adjust threshold as needed
            lastSpeechTime = System.currentTimeMillis();
        }
    }
    
    @Override
    public void onBufferReceived(byte[] buffer) {
        // Audio buffer received - speech is active
        lastSpeechTime = System.currentTimeMillis();
    }
    
    @Override
    public void onEndOfSpeech() {
        Log.d(TAG, "End of speech detected");
        // Don't immediately restart - wait for results first
    }
    
    @Override
    public void onError(int error) {
        String errorMessage = getErrorMessage(error);
        Log.e(TAG, "Speech recognition error: " + errorMessage);
        
        // Handle different error types
        switch (error) {
            case SpeechRecognizer.ERROR_AUDIO:
            case SpeechRecognizer.ERROR_NETWORK:
            case SpeechRecognizer.ERROR_NETWORK_TIMEOUT:
            case SpeechRecognizer.ERROR_SERVER:
                // Recoverable errors - attempt restart
                if (shouldContinue) {
                    backgroundHandler.postDelayed(() -> {
                        initializeSpeechRecognizer();
                        restartListening();
                    }, 1000);
                }
                break;
                
            case SpeechRecognizer.ERROR_NO_MATCH:
                // No speech detected - restart immediately
                if (shouldContinue) {
                    restartListening();
                }
                break;
                
            case SpeechRecognizer.ERROR_SPEECH_TIMEOUT:
                // Timeout reached - this is expected, restart
                if (shouldContinue) {
                    restartListening();
                }
                break;
                
            default:
                // Other errors - notify callback but attempt recovery
                handleError(errorMessage);
                if (shouldContinue) {
                    restartListening();
                }
                break;
        }
    }
    
    @Override
    public void onResults(Bundle results) {
        handleResults(results, true);
    }
    
    @Override
    public void onPartialResults(Bundle partialResults) {
        handleResults(partialResults, false);
    }
    
    private void handleResults(Bundle results, boolean isFinal) {
        ArrayList<String> matches = results.getStringArrayList(
            SpeechRecognizer.RESULTS_RECOGNITION);
        
        if (matches != null && !matches.isEmpty()) {
            String recognizedText = matches.get(0);
            
            if (isFinal) {
                // Final result - add to permanent text
                if (!recognizedText.trim().isEmpty()) {
                    // Avoid duplicating text that might already be in finalText
                    String newText = cleanAndDeduplicateText(recognizedText);
                    if (!newText.trim().isEmpty()) {
                        if (finalText.length() > 0) {
                            finalText.append(" ");
                        }
                        finalText.append(newText);
                        lastConfirmedText = newText;
                    }
                }
                
                currentPartialText = "";
                
                // Update UI with final text
                updateUI();
                
                // Restart for continuous recognition
                if (shouldContinue) {
                    restartListening();
                }
                
            } else {
                // Partial result - update current partial text
                currentPartialText = recognizedText;
                updateUI();
                lastSpeechTime = System.currentTimeMillis();
            }
        } else if (isFinal) {
            // No results but final - restart anyway
            if (shouldContinue) {
                restartListening();
            }
        }
    }
    
    private String cleanAndDeduplicateText(String newText) {
        // Remove text that might already be included in finalText
        if (lastConfirmedText != null && !lastConfirmedText.isEmpty()) {
            String finalTextStr = finalText.toString().toLowerCase();
            String newTextLower = newText.toLowerCase();
            String lastConfirmedLower = lastConfirmedText.toLowerCase();
            
            // If the new text starts with the last confirmed text, remove the duplicate
            if (newTextLower.startsWith(lastConfirmedLower)) {
                newText = newText.substring(lastConfirmedText.length()).trim();
            }
        }
        
        return newText.trim();
    }
    
    private void updateUI() {
        mainHandler.post(() -> {
            if (callback != null) {
                String fullText = finalText.toString();
                callback.onTextUpdate(fullText, currentPartialText);
            }
        });
    }
    
    private void handleError(String errorMessage) {
        mainHandler.post(() -> {
            if (callback != null) {
                callback.onError(errorMessage);
            }
        });
    }
    
    private String getErrorMessage(int error) {
        switch (error) {
            case SpeechRecognizer.ERROR_AUDIO:
                return "Audio recording error";
            case SpeechRecognizer.ERROR_CLIENT:
                return "Client side error";
            case SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS:
                return "Insufficient permissions";
            case SpeechRecognizer.ERROR_NETWORK:
                return "Network error";
            case SpeechRecognizer.ERROR_NETWORK_TIMEOUT:
                return "Network timeout";
            case SpeechRecognizer.ERROR_NO_MATCH:
                return "No match found";
            case SpeechRecognizer.ERROR_RECOGNIZER_BUSY:
                return "Recognition service busy";
            case SpeechRecognizer.ERROR_SERVER:
                return "Server error";
            case SpeechRecognizer.ERROR_SPEECH_TIMEOUT:
                return "Speech input timeout";
            default:
                return "Unknown error";
        }
    }
    
    public String getFinalText() {
        return finalText.toString();
    }
    
    public boolean isCurrentlyListening() {
        return isListening && shouldContinue;
    }
    
    public void cleanup() {
        stopListening();
        
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
            speechRecognizer = null;
        }
        
        if (backgroundThread != null) {
            backgroundThread.quitSafely();
            try {
                backgroundThread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
}


public class MainActivity extends AppCompatActivity {
    private ContinuousSpeechRecognizer speechRecognizer;
    private TextView finalTextView;
    private TextView partialTextView;
    private TextView statusView;
    private Button startButton;
    private Button stopButton;
    private ProgressBar progressBar;
    
    // Permissions
    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final String[] REQUIRED_PERMISSIONS = {
        Manifest.permission.RECORD_AUDIO
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initializeViews();
        checkPermissions();
    }
    
    private void initializeViews() {
        finalTextView = findViewById(R.id.finalTextView);
        partialTextView = findViewById(R.id.partialTextView);
        statusView = findViewById(R.id.statusView);
        startButton = findViewById(R.id.startButton);
        stopButton = findViewById(R.id.stopButton);
        progressBar = findViewById(R.id.progressBar);
        
        startButton.setOnClickListener(v -> startContinuousRecognition());
        stopButton.setOnClickListener(v -> stopContinuousRecognition());
        
        updateButtonStates(false);
    }
    
    private void checkPermissions() {
        if (!hasRequiredPermissions()) {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, PERMISSION_REQUEST_CODE);
        } else {
            initializeSpeechRecognizer();
        }
    }
    
    private boolean hasRequiredPermissions() {
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) 
                != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (hasRequiredPermissions()) {
                initializeSpeechRecognizer();
            } else {
                showPermissionError();
            }
        }
    }
    
    private void initializeSpeechRecognizer() {
        if (!SpeechRecognizer.isRecognitionAvailable(this)) {
            showError("Speech recognition not available on this device");
            return;
        }
        
        speechRecognizer = new ContinuousSpeechRecognizer(this, new SpeechRecognitionCallback() {
            @Override
            public void onTextUpdate(String finalText, String partialText) {
                updateTextDisplay(finalText, partialText);
            }
            
            @Override
            public void onError(String error) {
                showError("Recognition error: " + error);
            }
            
            @Override
            public void onStatusChange(boolean isListening) {
                updateStatus(isListening);
                updateButtonStates(isListening);
            }
            
            @Override
            public void onSilenceDetected() {
                // Optional: Show user that silence was detected
                statusView.setText("Silence detected - still listening...");
            }
        });
    }
    
    private void startContinuousRecognition() {
        if (speechRecognizer != null) {
            // Clear previous text
            finalTextView.setText("");
            partialTextView.setText("");
            
            speechRecognizer.startListening();
            statusView.setText("Starting speech recognition...");
        }
    }
    
    private void stopContinuousRecognition() {
        if (speechRecognizer != null) {
            speechRecognizer.stopListening();
            statusView.setText("Speech recognition stopped");
        }
    }
    
    private void updateTextDisplay(String finalText, String partialText) {
        finalTextView.setText(finalText);
        
        // Show partial text in a different style
        if (partialText != null && !partialText.isEmpty()) {
            SpannableString spannablePartial = new SpannableString(partialText);
            spannablePartial.setSpan(new StyleSpan(Typeface.ITALIC), 0, partialText.length(), 
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannablePartial.setSpan(new ForegroundColorSpan(Color.GRAY), 0, partialText.length(), 
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            partialTextView.setText(spannablePartial);
        } else {
            partialTextView.setText("");
        }
        
        // Auto-scroll to bottom if text is long
        scrollToBottom();
    }
    
    private void scrollToBottom() {
        if (finalTextView.getLayout() != null) {
            int scrollAmount = finalTextView.getLayout().getLineTop(finalTextView.getLineCount()) 
                - finalTextView.getHeight();
            if (scrollAmount > 0) {
                finalTextView.scrollTo(0, scrollAmount);
            }
        }
    }
    
    private void updateStatus(boolean isListening) {
        if (isListening) {
            statusView.setText("Listening...");
            progressBar.setVisibility(View.VISIBLE);
        } else {
            statusView.setText("Not listening");
            progressBar.setVisibility(View.GONE);
        }
    }
    
    private void updateButtonStates(boolean isListening) {
        startButton.setEnabled(!isListening);
        stopButton.setEnabled(isListening);
    }
    
    private void showError(String error) {
        Toast.makeText(this, error, Toast.LENGTH_LONG).show();
        statusView.setText("Error: " + error);
        updateButtonStates(false);
    }
    
    private void showPermissionError() {
        new AlertDialog.Builder(this)
            .setTitle("Permission Required")
            .setMessage("This app requires microphone permission to function properly.")
            .setPositiveButton("Grant Permission", (dialog, which) -> checkPermissions())
            .setNegativeButton("Cancel", (dialog, which) -> finish())
            .setCancelable(false)
            .show();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (speechRecognizer != null) {
            speechRecognizer.cleanup();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // Optionally stop recognition when app goes to background
        if (speechRecognizer != null && speechRecognizer.isCurrentlyListening()) {
            speechRecognizer.stopListening();
        }
    }
    
    // Optional: Save and restore recognized text
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (speechRecognizer != null) {
            outState.putString("recognized_text", speechRecognizer.getFinalText());
        }
    }
    
    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        String savedText = savedInstanceState.getString("recognized_text");
        if (savedText != null) {
            finalTextView.setText(savedText);
        }
    }
}

