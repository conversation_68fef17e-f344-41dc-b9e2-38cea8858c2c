import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import '../../../core/services/continuous_speech_service.dart';

/// Enhanced modal dialog for seamless continuous voice recording
/// Implements the seamless restart mechanism inspired by Android's ContinuousSpeechRecognizer
class ContinuousRecordingModal extends StatefulWidget {
  const ContinuousRecordingModal({super.key});

  @override
  State<ContinuousRecordingModal> createState() => _ContinuousRecordingModalState();
}

class _ContinuousRecordingModalState extends State<ContinuousRecordingModal>
    with TickerProviderStateMixin {
  final ContinuousSpeechService _speechService = ContinuousSpeechService();

  // Recording states - SEAMLESS CONTINUOUS MODE
  bool _isRecording = false;
  bool _isInitialized = false;
  bool _isStartingRecording = false;
  bool _silenceDetected = false;

  // Text management - following the Android pattern
  String _finalText = '';
  String _partialText = '';
  String _error = '';

  // Timing
  int _recordingSeconds = 0;
  Timer? _recordingTimer;

  // Animation controllers
  late AnimationController _micPulseController;
  late Animation<double> _micPulseAnimation;
  late AnimationController _silenceIndicatorController;
  late Animation<double> _silenceIndicatorAnimation;

  // Enhanced waveform visualization
  List<double> _waveformHeights = List.filled(32, 4.0);
  double _currentSoundLevel = 0.0;

  // UI state
  bool _showBlink = true;
  Timer? _blinkTimer;
  Color _recordingColor = Colors.red.shade600;

  @override
  void initState() {
    super.initState();

    // Set up microphone pulse animation when recording
    _micPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true);
    _micPulseAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _micPulseController, curve: Curves.easeInOut),
    );

    // Set up silence indicator animation
    _silenceIndicatorController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _silenceIndicatorAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _silenceIndicatorController, curve: Curves.easeInOut),
    );

    // Set up blinking animation for recording indicator
    _blinkTimer = Timer.periodic(const Duration(milliseconds: 600), (timer) {
      if (mounted) {
        setState(() {
          _showBlink = !_showBlink;
        });
      }
    });

    // Initialize speech and start continuous recording automatically
    _initializeAndStartContinuous();
  }

  @override
  void dispose() {
    _stopRecording();
    _recordingTimer?.cancel();
    _micPulseController.dispose();
    _silenceIndicatorController.dispose();
    _blinkTimer?.cancel();
    _speechService.dispose();
    super.dispose();
  }

  /// Initialize and start seamless continuous recording
  Future<void> _initializeAndStartContinuous() async {
    setState(() {
      _isStartingRecording = true;
      _error = '';
    });

    try {
      final available = await _speechService.initialize();
      if (available) {
        setState(() {
          _isInitialized = true;
          _error = '';
        });

        // Start continuous recording automatically
        _startContinuousRecording();
      } else {
        setState(() {
          _isStartingRecording = false;
          _error = 'Speech recognition not available on this device';
          _isInitialized = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing speech: $e');
      }
      setState(() {
        _isStartingRecording = false;
        _error = 'Error initializing speech recognition';
        _isInitialized = false;
      });
    }
  }

  /// Start seamless continuous recording
  void _startContinuousRecording() async {
    if (!_isInitialized) {
      setState(() {
        _error = 'Speech recognition not initialized';
      });
      return;
    }

    setState(() {
      _isStartingRecording = true;
      _silenceDetected = false;
      _error = '';
    });

    // Start haptic feedback
    HapticFeedback.mediumImpact();

    final success = await _speechService.startContinuousListening(
      onTextUpdate: _onTextUpdate,
      onStatus: _onStatus,
      onError: _onError,
      onSoundLevel: _onSoundLevelChange,
      onSilenceDetected: _onSilenceDetected,
    );

    if (success) {
      setState(() {
        _isStartingRecording = false;
        _isRecording = true;
        _error = '';
        _recordingSeconds = 0;
      });

      // Start recording timer
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _recordingSeconds++;

            // Change color every 15 seconds for visual feedback
            if (_recordingSeconds % 15 == 0) {
              _recordingColor = _recordingSeconds % 30 == 0 ?
                  Colors.red.shade700 : Colors.orange.shade700;
            }
          });
        }
      });
    } else {
      setState(() {
        _isStartingRecording = false;
        _error = 'Failed to start continuous speech recognition';
      });
    }
  }

  /// Handle text updates from continuous recognition
  void _onTextUpdate(String finalText, String partialText) {
    if (kDebugMode) {
      print('🎤 Text update - Final: "$finalText", Partial: "$partialText"');
    }

    if (mounted) {
      setState(() {
        _finalText = finalText;
        _partialText = partialText;

        // Clear error when we get results
        if (finalText.isNotEmpty || partialText.isNotEmpty) {
          _error = '';
          _silenceDetected = false;
          _silenceIndicatorController.reset();
        }
      });
    }
  }

  /// Handle status changes
  void _onStatus(String status) {
    if (kDebugMode) {
      print('Continuous speech status: $status');
    }

    // Status is handled internally by the continuous service
    // UI remains stable during seamless restarts
  }

  /// Handle errors with graceful recovery
  void _onError(String error) {
    if (kDebugMode) {
      print('Continuous speech error: $error');
    }

    if (mounted) {
      setState(() {
        // Don't show errors for automatic recovery attempts
        // Only show persistent errors
        if (!error.contains('timeout') && !error.contains('no match')) {
          _error = 'Recognition issue: $error';
        }
      });
    }
  }

  /// Handle silence detection
  void _onSilenceDetected() {
    if (kDebugMode) {
      print('🔇 Silence detected - but still listening continuously');
    }

    if (mounted) {
      setState(() {
        _silenceDetected = true;
      });

      // Animate silence indicator
      _silenceIndicatorController.forward().then((_) {
        if (mounted) {
          Timer(const Duration(seconds: 2), () {
            if (mounted) {
              setState(() {
                _silenceDetected = false;
              });
              _silenceIndicatorController.reverse();
            }
          });
        }
      });
    }
  }

  /// Handle sound level changes for waveform
  void _onSoundLevelChange(double level) {
    _currentSoundLevel = level;
    _updateWaveform();
  }

  /// Stop continuous recording
  void _stopRecording() {
    if (_isRecording) {
      _speechService.stopListening();
      _recordingTimer?.cancel();
      setState(() {
        _isRecording = false;
        _silenceDetected = false;
        _waveformHeights = List.filled(32, 8.0);
        _currentSoundLevel = 0.0;
      });

      if (kDebugMode) {
        print('🛑 Continuous recording stopped');
      }
    }
  }

  /// Save the transcript and close
  void _saveTranscript() {
    _stopRecording();
    final textToSave = _speechService.combinedText;
    Navigator.of(context).pop(textToSave);
  }

  /// Cancel recording and close
  void _cancelRecording() {
    _stopRecording();
    Navigator.of(context).pop();
  }

  /// Update waveform visualization
  void _updateWaveform() {
    if (!mounted) return;

    setState(() {
      const int numBars = 32;
      const double baseHeight = 8.0;
      final double maxAmplitude = 24.0 * _currentSoundLevel.clamp(0.0, 1.0);

      _waveformHeights = List.generate(numBars, (i) {
        double position = i / (numBars - 1);
        double bellCurve = 1.0 - (2.0 * position - 1.0) * (2.0 * position - 1.0);
        double randomFactor = 0.7 + (0.3 * (DateTime.now().millisecondsSinceEpoch % (i+5)) / 5);
        double height = baseHeight + (maxAmplitude * bellCurve * randomFactor);
        return height.clamp(4.0, 36.0);
      });
    });
  }

  /// Format recording time
  String _formatRecordingTime() {
    final minutes = (_recordingSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_recordingSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(LucideIcons.x),
            onPressed: _cancelRecording,
          ),
          title: Row(
            children: [
              Icon(
                _isRecording ? LucideIcons.mic : LucideIcons.mic,
                color: _isRecording ? _recordingColor : null,
              ),
              const SizedBox(width: 8),
              Text(
                _isRecording
                    ? 'Continuous Recording - Seamless'
                    : _isStartingRecording
                        ? 'Starting Continuous Mode...'
                        : _finalText.isNotEmpty || _partialText.isNotEmpty
                            ? 'Recording Complete'
                            : _isInitialized
                                ? 'No Speech Detected'
                                : 'Voice Input Unavailable',
                style: TextStyle(
                  color: _isRecording ? _recordingColor : null,
                  fontWeight: _isRecording ? FontWeight.bold : null,
                ),
              ),
            ],
          ),
          actions: [
            // Save button when there's text
            if (_isInitialized && (_finalText.isNotEmpty || _partialText.isNotEmpty))
              TextButton(
                onPressed: _saveTranscript,
                child: Text(
                  'Save',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Enhanced recording indicator with seamless feedback
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      // Outer ripple effect for continuous recording
                      if (_isRecording)
                        AnimatedBuilder(
                          animation: _micPulseController,
                          builder: (context, child) {
                            return Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _recordingColor.withValues(
                                  alpha: 0.1 * _micPulseAnimation.value,
                                ),
                              ),
                            );
                          },
                        ),

                      // Silence detection indicator
                      if (_silenceDetected)
                        AnimatedBuilder(
                          animation: _silenceIndicatorAnimation,
                          builder: (context, child) {
                            return Container(
                              width: 140,
                              height: 140,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.orange.withValues(
                                    alpha: 0.5 * _silenceIndicatorAnimation.value,
                                  ),
                                  width: 2.0,
                                ),
                              ),
                            );
                          },
                        ),

                      // Main recording indicator
                      AnimatedBuilder(
                        animation: _micPulseController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _isRecording
                                ? _micPulseAnimation.value
                                : (_isStartingRecording ? 0.95 : 1.0),
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _isRecording
                                    ? _recordingColor.withValues(alpha: 0.2)
                                    : _isStartingRecording
                                        ? Colors.orange.withValues(alpha: 0.2)
                                        : _finalText.isNotEmpty || _partialText.isNotEmpty
                                            ? Colors.green.shade100
                                            : Theme.of(context).colorScheme.primaryContainer,
                                border: Border.all(
                                  color: _isRecording
                                      ? _recordingColor
                                      : _isStartingRecording
                                          ? Colors.orange
                                          : _finalText.isNotEmpty || _partialText.isNotEmpty
                                              ? Colors.green.shade600
                                              : Theme.of(context).colorScheme.primary,
                                  width: 2.0,
                                ),
                              ),
                              child: Center(
                                child: Icon(
                                  _isRecording
                                      ? LucideIcons.mic
                                      : _isStartingRecording
                                          ? LucideIcons.loader
                                          : _finalText.isNotEmpty || _partialText.isNotEmpty
                                              ? LucideIcons.checkCircle
                                              : (_isInitialized ? LucideIcons.mic : LucideIcons.micOff),
                                  color: _isRecording
                                      ? _recordingColor
                                      : _isStartingRecording
                                          ? Colors.orange
                                          : _finalText.isNotEmpty || _partialText.isNotEmpty
                                              ? Colors.green.shade600
                                              : Theme.of(context).colorScheme.onPrimaryContainer,
                                  size: 40.0,
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      // Loading indicator when starting
                      if (_isStartingRecording)
                        const CircularProgressIndicator(),
                    ],
                  ),

                  // Continuous recording status
                  if (_isRecording)
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0, bottom: 2.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Blinking recording indicator
                          AnimatedOpacity(
                            opacity: _showBlink ? 1.0 : 0.2,
                            duration: const Duration(milliseconds: 300),
                            child: Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: _recordingColor,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: _recordingColor.withValues(alpha: 0.5),
                                    blurRadius: 4,
                                    spreadRadius: 1,
                                  )
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Continuous Recording...',
                            style: TextStyle(
                              color: _recordingColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Silence detection indicator
                  if (_silenceDetected)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0, bottom: 2.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            LucideIcons.volume1,
                            color: Colors.orange.shade600,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Silence detected - still listening...',
                            style: TextStyle(
                              color: Colors.orange.shade600,
                              fontSize: 14,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Recording time
                  if (_isRecording)
                    Padding(
                      padding: const EdgeInsets.only(top: 2.0, bottom: 2.0),
                      child: Text(
                        _formatRecordingTime(),
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),

                  // Enhanced waveform for continuous recording
                  if (_isRecording)
                    Container(
                      margin: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                      height: 40,
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          final availableWidth = constraints.maxWidth;
                          final barCount = (availableWidth / 10).floor().clamp(8, 24);
                          final barWidth = (availableWidth / barCount) - 4;

                          return Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(barCount, (index) {
                              final rawHeight = _waveformHeights.length > index
                                  ? _waveformHeights[index]
                                  : 8.0;
                              final height = rawHeight.clamp(4.0, 36.0);
                              final safeBarWidth = barWidth.clamp(2.0, 20.0);

                              return Container(
                                width: safeBarWidth,
                                height: height,
                                margin: const EdgeInsets.symmetric(horizontal: 1),
                                decoration: BoxDecoration(
                                  color: _recordingColor.withValues(
                                    alpha: 0.7 + (0.3 * height / 40),
                                  ),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              );
                            }),
                          );
                        },
                      ),
                    ),

                  // Transcript display with seamless updates
                  Container(
                    margin: const EdgeInsets.only(top: 16.0),
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(
                        color: _isRecording
                            ? _recordingColor.withValues(alpha: 0.3)
                            : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                        width: 1.0,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with continuous status
                        Row(
                          children: [
                            Icon(
                              _isRecording
                                  ? LucideIcons.mic
                                  : _finalText.isNotEmpty || _partialText.isNotEmpty
                                      ? LucideIcons.checkCircle
                                      : LucideIcons.micOff,
                              size: 16,
                              color: _isRecording
                                  ? _recordingColor
                                  : _finalText.isNotEmpty || _partialText.isNotEmpty
                                      ? Colors.green.shade600
                                      : Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              _isRecording
                                  ? 'Continuous speech recognition active...'
                                  : _finalText.isNotEmpty || _partialText.isNotEmpty
                                      ? 'Speech captured seamlessly'
                                      : 'No speech detected',
                              style: TextStyle(
                                fontSize: 12,
                                color: _isRecording
                                    ? _recordingColor
                                    : _finalText.isNotEmpty || _partialText.isNotEmpty
                                        ? Colors.green.shade600
                                        : Theme.of(context).colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Seamless transcript content
                        if (_finalText.isNotEmpty || _partialText.isNotEmpty)
                          RichText(
                            text: TextSpan(
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                                fontSize: 16,
                                height: 1.4,
                              ),
                              children: [
                                // Final text (confirmed)
                                if (_finalText.isNotEmpty)
                                  TextSpan(
                                    text: _finalText,
                                    style: const TextStyle(fontWeight: FontWeight.normal),
                                  ),
                                // Space between final and partial
                                if (_finalText.isNotEmpty && _partialText.isNotEmpty)
                                  const TextSpan(text: ' '),
                                // Partial text (live transcription)
                                if (_partialText.isNotEmpty)
                                  TextSpan(
                                    text: _partialText,
                                    style: TextStyle(
                                      fontStyle: FontStyle.italic,
                                      color: Theme.of(context).colorScheme.onSurfaceVariant
                                          .withValues(alpha: 0.8),
                                    ),
                                  ),
                              ],
                            ),
                          )
                        else if (_isRecording)
                          Text(
                            'Speak naturally and your words will appear here continuously...',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurfaceVariant
                                  .withValues(alpha: 0.7),
                              fontSize: 14,
                              fontStyle: FontStyle.italic,
                            ),
                          )
                        else
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'No speech was captured.',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Tips for continuous recording:\n• Speak naturally at normal pace\n• No need to pause between sentences\n• The system will capture everything seamlessly\n• Check microphone permissions if needed',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant
                                      .withValues(alpha: 0.8),
                                  fontSize: 12,
                                  height: 1.4,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),

                  // Error message (only for persistent errors)
                  if (_error.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 16.0),
                      padding: const EdgeInsets.all(8.0),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.errorContainer,
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Text(
                        _error,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 1.0,
              ),
            ),
          ),
          child: Row(
            children: [
              // Cancel/Close button
              Expanded(
                child: OutlinedButton(
                  onPressed: _cancelRecording,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                  ),
                  child: Text(_isRecording ? 'Cancel' : 'Close'),
                ),
              ),

              const SizedBox(width: 16),

              // Main action button - SEAMLESS EXPERIENCE
              if (_isRecording)
                // STOP RECORDING - Single button for seamless mode
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _stopRecording,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(LucideIcons.square, size: 18),
                        SizedBox(width: 8),
                        Text('Stop Recording', style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                )
              else if (_finalText.isNotEmpty || _partialText.isNotEmpty)
                // SEND BUTTON - When we have captured text
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveTranscript,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                    child: const Text('Send'),
                  ),
                )
              else
                // TRY AGAIN - When no speech detected
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _finalText = '';
                        _partialText = '';
                        _error = '';
                      });
                      _startContinuousRecording();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(LucideIcons.mic, size: 18),
                        SizedBox(width: 8),
                        Text('Try Again'),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
